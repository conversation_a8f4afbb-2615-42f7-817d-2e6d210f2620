[Locations]
# Path to loadout config. Can use relative path from here.
looks_config = ./appearance.cfg

# Path to python file. Can use relative path from here.
python_file = ./bot.py
requirements_file = ./requirements.txt

logo_file = ./necto_logo.png

# Name of the bot in-game
name = niggatron

# The maximum number of ticks per second that your bot wishes to receive.
maximum_tick_rate_preference = 120

[Details]
# These values are optional but useful metadata for helper programs
# Name of the bot's creator/developer
developer = X/Z

# Short description of the bot
description = Lebron James

# Programming language
language = rlgym

tags = 1v1, teamplay
